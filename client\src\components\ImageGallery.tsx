import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON>alogContent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Input } from "@/components/ui/input";
import { Search, Image as ImageIcon, Calendar, FileSize } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface ImageFile {
  filename: string;
  url: string;
  size: number;
  uploadedAt: string;
  modifiedAt: string;
}

interface ImageGalleryProps {
  onImageSelect: (url: string) => void;
  trigger?: React.ReactNode;
}

export default function ImageGallery({ onImageSelect, trigger }: ImageGalleryProps) {
  const [images, setImages] = useState<ImageFile[]>([]);
  const [filteredImages, setFilteredImages] = useState<ImageFile[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const { toast } = useToast();

  const fetchImages = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/upload/list/images', {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to fetch images');
      }

      const data = await response.json();
      setImages(data.images || []);
      setFilteredImages(data.images || []);
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to load images",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchImages();
    }
  }, [isOpen]);

  useEffect(() => {
    if (searchTerm) {
      const filtered = images.filter(image =>
        image.filename.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredImages(filtered);
    } else {
      setFilteredImages(images);
    }
  }, [searchTerm, images]);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleImageSelect = (url: string) => {
    onImageSelect(url);
    setIsOpen(false);
    toast({
      title: "Image selected",
      description: "The image has been selected successfully"
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <ImageIcon className="h-4 w-4 mr-2" />
            Browse Images
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle>Select an Image</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search images by filename..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Images Grid */}
          <ScrollArea className="h-[500px]">
            {isLoading ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                  <p className="text-sm text-gray-500">Loading images...</p>
                </div>
              </div>
            ) : filteredImages.length === 0 ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">
                    {searchTerm ? 'No images found matching your search' : 'No images uploaded yet'}
                  </p>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-2">
                {filteredImages.map((image) => (
                  <div
                    key={image.filename}
                    className="group relative border rounded-lg overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => handleImageSelect(image.url)}
                  >
                    <div className="aspect-square overflow-hidden">
                      <img
                        src={image.url}
                        alt={image.filename}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                        loading="lazy"
                      />
                    </div>
                    
                    {/* Image Info Overlay */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-60 transition-all duration-200 flex items-end">
                      <div className="p-2 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200 w-full">
                        <p className="text-xs font-medium truncate mb-1">{image.filename}</p>
                        <div className="flex items-center justify-between text-xs">
                          <span className="flex items-center">
                            <FileSize className="h-3 w-3 mr-1" />
                            {formatFileSize(image.size)}
                          </span>
                          <span className="flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            {formatDate(image.uploadedAt)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog>
  );
}
