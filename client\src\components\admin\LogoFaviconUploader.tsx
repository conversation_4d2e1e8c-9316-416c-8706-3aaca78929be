import { useState, useRef, ChangeEvent } from "react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Upload, X, Image as ImageIcon, FolderOpen } from "lucide-react";
import ImageGallery from "../ImageGallery";

interface LogoFaviconUploaderProps {
  initialUrl?: string;
  onImageUploaded: (url: string) => void;
  type: "logo" | "favicon";
  label: string;
}

export default function LogoFaviconUploader({
  initialUrl,
  onImageUploaded,
  type,
  label
}: LogoFaviconUploaderProps) {
  const [imageUrl, setImageUrl] = useState<string>(initialUrl || "");
  const [isUploading, setIsUploading] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const handleFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      await uploadFile(file);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      await uploadFile(files[0]);
    }
  };

  const uploadFile = async (file: File) => {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "Invalid file type",
        description: "Please upload a JPEG, PNG, GIF, WebP, or SVG image",
        variant: "destructive"
      });
      return;
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Please upload an image smaller than 5MB",
        variant: "destructive"
      });
      return;
    }

    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch('/api/upload/image', {
        method: 'POST',
        body: formData,
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to upload image');
      }

      const data = await response.json();
      setImageUrl(data.url);
      onImageUploaded(data.url);

      toast({
        title: "Image uploaded",
        description: `${label} uploaded successfully`
      });
    } catch (error) {
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "Something went wrong",
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveImage = () => {
    setImageUrl("");
    onImageUploaded("");
  };

  const handleImageSelect = (url: string) => {
    setImageUrl(url);
    onImageUploaded(url);
  };

  const handleUrlChange = (url: string) => {
    setImageUrl(url);
    onImageUploaded(url);
  };

  const getRecommendations = () => {
    if (type === "logo") {
      return {
        size: "200x50px or similar aspect ratio",
        formats: "PNG, JPG, SVG",
        description: "Logo displayed in the header"
      };
    } else {
      return {
        size: "32x32px or 16x16px",
        formats: "ICO, PNG, SVG",
        description: "Small icon displayed in browser tabs and bookmarks"
      };
    }
  };

  const recommendations = getRecommendations();

  return (
    <div className="space-y-4">
      {/* Current Image Preview */}
      {imageUrl && (
        <div className="flex items-center space-x-4 p-4 border rounded-lg bg-gray-50">
          <img
            src={imageUrl}
            alt={`Current ${type}`}
            className={type === "logo" ? "h-12 w-auto max-w-[200px] object-contain" : "h-8 w-8 object-contain"}
            onError={(e) => {
              e.currentTarget.style.display = 'none';
            }}
          />
          <div className="flex-1">
            <p className="text-sm font-medium">Current {label}</p>
            <p className="text-xs text-gray-500 break-all">{imageUrl}</p>
          </div>
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleRemoveImage}
          >
            Remove
          </Button>
        </div>
      )}

      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          isDragging
            ? 'border-blue-500 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          className="hidden"
        />

        <div className="space-y-4">
          <div className="flex justify-center">
            <div className="p-3 bg-gray-100 rounded-full">
              <Upload className="h-6 w-6 text-gray-600" />
            </div>
          </div>

          <div>
            <p className="text-sm font-medium text-gray-900">
              Drop your {type} here, or{" "}
              <button
                type="button"
                className="text-blue-600 hover:text-blue-500"
                onClick={() => fileInputRef.current?.click()}
                disabled={isUploading}
              >
                browse
              </button>
            </p>
            <p className="text-xs text-gray-500 mt-1">
              {recommendations.formats} up to 5MB
            </p>
          </div>

          {isUploading && (
            <div className="flex items-center justify-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span className="text-sm text-gray-600">Uploading...</span>
            </div>
          )}
        </div>
      </div>

      {/* Browse Existing Images */}
      <div className="flex justify-center">
        <ImageGallery
          onImageSelect={handleImageSelect}
          trigger={
            <Button variant="outline" size="sm" type="button">
              <FolderOpen className="h-4 w-4 mr-2" />
              Browse Existing Images
            </Button>
          }
        />
      </div>

      {/* URL Input Alternative */}
      <div className="space-y-2">
        <p className="text-sm font-medium text-gray-700">Or enter a URL:</p>
        <input
          type="url"
          placeholder={`https://example.com/${type}.${type === 'favicon' ? 'ico' : 'png'}`}
          value={imageUrl}
          onChange={(e) => handleUrlChange(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* Recommendations */}
      <div className="text-sm text-gray-500 space-y-1">
        <p>• Recommended size: {recommendations.size}</p>
        <p>• Supported formats: {recommendations.formats}</p>
        <p>• {recommendations.description}</p>
      </div>
    </div>
  );
}
