import { Router, Request, Response } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { randomBytes } from 'crypto';
import { isAdmin } from '../middleware/auth';

const uploadRouter = Router();

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(process.cwd(), 'uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configure multer storage
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    const randomId = randomBytes(8).toString('hex');
    const fileExt = path.extname(file.originalname);
    cb(null, `${Date.now()}-${randomId}${fileExt}`);
  }
});

// File filter to only allow image files
const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.'));
  }
};

// Configure multer upload
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB max file size
  },
  fileFilter: fileFilter
});

// Image upload endpoint
uploadRouter.post('/image', upload.single('image'), (req: Request, res: Response) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No image file provided' });
    }

    // Get the server URL
    const protocol = req.protocol;
    const host = req.get('host');
    const baseUrl = `${protocol}://${host}`;

    // Create the image URL
    const imageUrl = `${baseUrl}/uploads/${req.file.filename}`;

    // Return the image URL
    res.status(200).json({
      url: imageUrl,
      filename: req.file.filename
    });
  } catch (error) {
    console.error('Error uploading image:', error);
    res.status(500).json({
      message: 'Failed to upload image',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Base64 image upload endpoint (fallback for browsers that don't support FormData)
uploadRouter.post('/image-base64', (req: Request, res: Response) => {
  try {
    const { imageData } = req.body;

    if (!imageData || !imageData.startsWith('data:image/')) {
      return res.status(400).json({ message: 'Valid image data is required' });
    }

    // Extract the image type and data
    const matches = imageData.match(/^data:image\/([a-zA-Z]+);base64,(.+)$/);

    if (!matches || matches.length !== 3) {
      return res.status(400).json({ message: 'Invalid image data format' });
    }

    const imageType = matches[1];
    const base64Data = matches[2];
    const buffer = Buffer.from(base64Data, 'base64');

    // Generate a random filename
    const randomId = randomBytes(8).toString('hex');
    const filename = `${Date.now()}-${randomId}.${imageType}`;
    const filePath = path.join(uploadsDir, filename);

    // Save the file
    fs.writeFileSync(filePath, buffer);

    // Get the server URL
    const protocol = req.protocol;
    const host = req.get('host');
    const baseUrl = `${protocol}://${host}`;

    // Create the image URL
    const imageUrl = `${baseUrl}/uploads/${filename}`;

    // Return the image URL
    res.status(200).json({
      url: imageUrl,
      filename: filename
    });
  } catch (error) {
    console.error('Error processing base64 image upload:', error);
    res.status(500).json({
      message: 'Failed to process image upload',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// List all uploaded images
uploadRouter.get('/list/images', (req: Request, res: Response) => {
  try {
    // Read all files from uploads directory
    const files = fs.readdirSync(uploadsDir);

    // Filter for image files and get their details
    const imageFiles = files
      .filter(file => {
        const ext = path.extname(file).toLowerCase();
        return ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'].includes(ext);
      })
      .map(filename => {
        const filePath = path.join(uploadsDir, filename);
        const stats = fs.statSync(filePath);

        // Get the server URL
        const protocol = req.protocol;
        const host = req.get('host');
        const baseUrl = `${protocol}://${host}`;

        return {
          filename,
          url: `${baseUrl}/uploads/${filename}`,
          size: stats.size,
          uploadedAt: stats.birthtime,
          modifiedAt: stats.mtime
        };
      })
      .sort((a, b) => b.uploadedAt.getTime() - a.uploadedAt.getTime()); // Sort by newest first

    res.status(200).json({
      images: imageFiles,
      total: imageFiles.length
    });
  } catch (error) {
    console.error('Error listing images:', error);
    res.status(500).json({
      message: 'Failed to list images',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Create sample images for testing (development only)
uploadRouter.post('/create-sample-images', (req: Request, res: Response) => {
  try {
    // Create a simple SVG image as a sample
    const sampleImages = [
      {
        filename: 'sample-logo.svg',
        content: `<svg width="200" height="100" xmlns="http://www.w3.org/2000/svg">
          <rect width="200" height="100" fill="#3b82f6"/>
          <text x="100" y="55" font-family="Arial" font-size="16" fill="white" text-anchor="middle">Sample Logo</text>
        </svg>`
      },
      {
        filename: 'sample-product.svg',
        content: `<svg width="300" height="200" xmlns="http://www.w3.org/2000/svg">
          <rect width="300" height="200" fill="#10b981"/>
          <text x="150" y="105" font-family="Arial" font-size="18" fill="white" text-anchor="middle">Sample Product</text>
        </svg>`
      },
      {
        filename: 'sample-banner.svg',
        content: `<svg width="400" height="150" xmlns="http://www.w3.org/2000/svg">
          <rect width="400" height="150" fill="#f59e0b"/>
          <text x="200" y="80" font-family="Arial" font-size="20" fill="white" text-anchor="middle">Sample Banner</text>
        </svg>`
      }
    ];

    const protocol = req.protocol;
    const host = req.get('host');
    const baseUrl = `${protocol}://${host}`;

    const createdImages = sampleImages.map(image => {
      const filePath = path.join(uploadsDir, image.filename);

      // Only create if it doesn't exist
      if (!fs.existsSync(filePath)) {
        fs.writeFileSync(filePath, image.content);
      }

      return {
        filename: image.filename,
        url: `${baseUrl}/uploads/${image.filename}`,
        size: Buffer.byteLength(image.content),
        uploadedAt: new Date(),
        modifiedAt: new Date()
      };
    });

    res.status(200).json({
      message: 'Sample images created successfully',
      images: createdImages
    });
  } catch (error) {
    console.error('Error creating sample images:', error);
    res.status(500).json({
      message: 'Failed to create sample images',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Serve uploaded images
uploadRouter.get('/:filename', (req: Request, res: Response) => {
  const filename = req.params.filename;
  const filePath = path.join(uploadsDir, filename);

  // Check if file exists
  if (!fs.existsSync(filePath)) {
    return res.status(404).json({ message: 'Image not found' });
  }

  // Send the file
  res.sendFile(filePath);
});

export default uploadRouter;
